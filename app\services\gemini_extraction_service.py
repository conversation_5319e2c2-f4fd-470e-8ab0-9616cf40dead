import json
import logging
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
import google.generativeai as genai

from app.config import settings
from app.schemas.enums import ContractType
from app.schemas.contract_extraction import ExtractedContractData
from app.services.extraction_status import extraction_status_manager

# Configure Gemini
genai.configure(api_key=settings.GEMINI_API_KEY)

logger = logging.getLogger(__name__)


class GeminiExtractionService:
    """
    Unified service for contract extraction using Gemini 2.5 Flash
    """
    
    def __init__(self):
        self.model = genai.GenerativeModel('gemini-2.5-flash')
        self.timeout_minutes = 3
    
    async def extract_contract_data(
        self,
        extraction_id: str,
        files: List[bytes],
        file_names: List[str],
        contract_type_hint: Optional[ContractType] = None
    ) -> Dict[str, Any]:
        """
        Main extraction method - processes files and extracts contract data
        """
        try:
            # Update status to processing
            extraction_status_manager.update_status(
                extraction_id,
                status="processing",
                progress=10
            )

            # Step 1: Send files directly to Gemini for processing
            logger.info(f"Processing {len(files)} files with Gemini 2.5 Flash")

            # Process each file with Gemini
            all_results = []
            for i, (file_content, file_name) in enumerate(zip(files, file_names)):
                try:
                    logger.info(f"Processing file {i+1}/{len(files)}: {file_name}")

                    # Send file directly to Gemini
                    gemini_result = await self._process_file_with_gemini(
                        file_content, file_name, contract_type_hint
                    )
                    all_results.append(gemini_result)

                    # Update progress
                    progress = 10 + (i + 1) * 60 // len(files)
                    extraction_status_manager.update_status(extraction_id, progress=progress)

                except Exception as e:
                    logger.error(f"Failed to process {file_name} with Gemini: {str(e)}")
                    continue

            if not all_results:
                raise ValueError("Could not process any uploaded files with Gemini")

            # Step 2: Combine results if multiple files
            extraction_status_manager.update_status(extraction_id, progress=80)

            if len(all_results) == 1:
                final_result = all_results[0]
            else:
                # Combine multiple file results
                final_result = self._combine_extraction_results(all_results)

            extraction_status_manager.update_status(extraction_id, progress=90)
            
            # Step 3: Parse and validate results
            parsed_result = self._parse_gemini_response(final_result)

            # Step 4: Determine final status
            missing_fields = self._get_missing_required_fields(parsed_result)
            
            if missing_fields:
                status = "partial"
                extraction_status_manager.update_status(
                    extraction_id,
                    status=status,
                    progress=100,
                    contract_data=parsed_result.dict(),
                    confidence_score=parsed_result.field_confidence.get('overall', 0.0),
                    missing_fields=missing_fields
                )
            else:
                status = "completed"
                extraction_status_manager.update_status(
                    extraction_id,
                    status=status,
                    progress=100,
                    contract_data=parsed_result.dict(),
                    confidence_score=parsed_result.field_confidence.get('overall', 0.0)
                )
            
            return {
                "status": status,
                "data": parsed_result.dict(),
                "missing_fields": missing_fields
            }
            
        except Exception as e:
            logger.error(f"Extraction failed for {extraction_id}: {str(e)}")
            
            # Determine specific error message
            error_msg = self._get_specific_error_message(e)
            
            extraction_status_manager.update_status(
                extraction_id,
                status="failed",
                progress=100,
                error_message=error_msg
            )
            
            return {
                "status": "failed",
                "error": error_msg
            }
    
    async def _process_file_with_gemini(
        self,
        file_content: bytes,
        file_name: str,
        contract_type_hint: Optional[ContractType] = None
    ) -> str:
        """Send file directly to Gemini 2.5 Flash for processing"""

        file_extension = file_name.lower().split('.')[-1]

        try:
            import base64

            # Determine MIME type
            if file_extension == 'pdf':
                mime_type = 'application/pdf'
            elif file_extension in ['docx']:
                mime_type = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
            elif file_extension in ['doc']:
                mime_type = 'application/msword'
            else:
                raise ValueError(f"Unsupported file type: {file_extension}")

            # Create file part using the correct format for google-generativeai
            file_part = {
                "inline_data": {
                    "mime_type": mime_type,
                    "data": base64.b64encode(file_content).decode('utf-8')
                }
            }

            # Build prompt
            prompt = self._build_extraction_prompt_for_file(contract_type_hint)

            # Send to Gemini with file - correct format for google-generativeai
            response = self.model.generate_content([file_part, {"text": prompt}])
            return response.text

        except Exception as e:
            raise ValueError(f"Gemini API error processing {file_name}: {str(e)}")

    def _combine_extraction_results(self, results: List[str]) -> str:
        """Combine multiple extraction results into one"""
        # For now, just return the first successful result
        # In the future, we could implement more sophisticated merging
        return results[0] if results else ""
    
    def _build_extraction_prompt_for_file(self, contract_type_hint: Optional[ContractType] = None) -> str:
        """Build the prompt for Gemini file processing"""

        base_prompt = f"""
You are an expert legal document analyzer specializing in football/soccer contracts.
Analyze the uploaded contract document and extract structured data using OCR and document understanding.

INSTRUCTIONS:
1. First, identify the contract type from these options:
   - mandate: Agent mandate/authorization
   - representation_agreement: Player representation contract
   - club_contract: Player to club contract
   - commission_agreement: Commission/fee agreement
   - commercial: Commercial/sponsorship agreement

2. Extract relevant fields based on the contract type:

For ALL contract types:
- start_date (YYYY-MM-DD format)
- end_date (YYYY-MM-DD format)
- player_name (full name)
- agent_name (if mentioned)
- notes (summary of key clauses and terms)

For representation_agreement:
- commission_percentage (%)
- exclusive (true/false)
- termination_fee (amount)

For mandate:
- coverage_region (list of countries/regions)
- exclusive (true/false)

For club_contract:
- club_name
- gross_salary (annual amount)
- signing_fee
- option_years (number)

For commission_agreement:
- commission_amounts (list with amounts and due dates)
- installments (payment schedule)

3. Provide confidence scores (0-100) for each extracted field
4. If a field cannot be found, set it to null
5. Include an overall confidence score

IMPORTANT: 
- Return ONLY valid JSON
- Use null for missing values
- Be conservative with confidence scores
- Include field_confidence object with scores for each field

{f"HINT: This appears to be a {contract_type_hint} contract." if contract_type_hint else ""}

Return the result as JSON in this exact format:
{{
    "contract_type": "mandate|representation_agreement|club_contract|commission_agreement|commercial",
    "start_date": "YYYY-MM-DD or null",
    "end_date": "YYYY-MM-DD or null",
    "player_name": "string or null",
    "agent_name": "string or null",
    "club_name": "string or null",
    "gross_salary": number or null,
    "signing_fee": number or null,
    "commission_percentage": number or null,
    "option_years": number or null,
    "coverage_region": ["region1", "region2"] or null,
    "exclusive": true/false or null,
    "termination_fee": number or null,
    "commission_amounts": [{"amount": number, "due_date": "YYYY-MM-DD", "description": "string"}] or null,
    "installments": [{"amount": number, "due_date": "YYYY-MM-DD"}] or null,
    "notes": "string summary of key terms",
    "field_confidence": {{
        "contract_type": 0-100,
        "start_date": 0-100,
        "end_date": 0-100,
        "player_name": 0-100,
        "overall": 0-100
    }}
}}
"""
        
        return base_prompt
    
    def _parse_gemini_response(self, response: str) -> ExtractedContractData:
        """Parse Gemini response into structured data"""
        try:
            # Extract JSON from response (in case there's extra text)
            json_start = response.find('{')
            json_end = response.rfind('}') + 1
            
            if json_start == -1 or json_end == 0:
                raise ValueError("No JSON found in Gemini response")
            
            json_str = response[json_start:json_end]
            data = json.loads(json_str)
            
            # Convert to our schema
            return ExtractedContractData(**data)
            
        except json.JSONDecodeError as e:
            raise ValueError(f"Invalid JSON in Gemini response: {str(e)}")
        except Exception as e:
            raise ValueError(f"Could not parse Gemini response: {str(e)}")
    
    def _get_missing_required_fields(self, data: ExtractedContractData) -> List[str]:
        """Determine which required fields are missing based on contract type"""
        missing = []
        
        # Basic required fields for all contracts
        if not data.contract_type:
            missing.append("contract_type")
        if not data.start_date:
            missing.append("start_date")
        if not data.end_date:
            missing.append("end_date")
        
        # Contract type specific requirements
        if data.contract_type == ContractType.club_contract:
            if not data.club_name:
                missing.append("club_name")
            if not data.gross_salary:
                missing.append("gross_salary")
        
        elif data.contract_type == ContractType.representation_agreement:
            if not data.commission_percentage:
                missing.append("commission_percentage")
        
        elif data.contract_type == ContractType.mandate:
            if not data.coverage_region:
                missing.append("coverage_region")
        
        elif data.contract_type == ContractType.commission_agreement:
            if not data.commission_amounts:
                missing.append("commission_amounts")
        
        return missing
    
    def _get_specific_error_message(self, error: Exception) -> str:
        """Convert generic errors to user-friendly messages"""
        error_str = str(error).lower()

        if "unsupported file type" in error_str:
            return "Unsupported file type. Please upload PDF or DOCX files only"
        elif "gemini api error" in error_str:
            if "processing" in error_str:
                return "Could not process document. The file may be corrupted, password-protected, or contain unsupported content"
            else:
                return "AI service temporarily unavailable. Please try again in a few moments"
        elif "no json found" in error_str or "invalid json" in error_str:
            return "Could not detect contract type or extract structured data from document. Please ensure the document contains a valid contract"
        elif "timeout" in error_str:
            return "Document processing timed out. Please try with a smaller file"
        elif "could not process any uploaded files" in error_str:
            return "Unable to process any of the uploaded files. Please check that the files are valid contracts and try again"
        else:
            return "An unexpected error occurred during processing. Please try again or contact support"


# Global instance
gemini_service = GeminiExtractionService()
