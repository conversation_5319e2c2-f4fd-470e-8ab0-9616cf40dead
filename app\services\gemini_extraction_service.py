import json
import logging
from typing import Dict, Any, Optional, List, Tuple
from datetime import datetime, timedelta
import google.generativeai as genai
from docx import Document
import PyPDF2
import io

from app.config import settings
from app.schemas.enums import ContractType
from app.schemas.contract_extraction import ExtractedContractData
from app.services.extraction_status import extraction_status_manager

# Configure Gemini
genai.configure(api_key=settings.GEMINI_API_KEY)

logger = logging.getLogger(__name__)


class GeminiExtractionService:
    """
    Unified service for contract extraction using Gemini 2.5 Flash
    """
    
    def __init__(self):
        self.model = genai.GenerativeModel('gemini-2.5-flash')
        self.timeout_minutes = 3
    
    async def extract_contract_data(
        self, 
        extraction_id: str, 
        files: List[bytes], 
        file_names: List[str],
        contract_type_hint: Optional[ContractType] = None
    ) -> Dict[str, Any]:
        """
        Main extraction method - processes files and extracts contract data
        """
        try:
            # Update status to processing
            extraction_status_manager.update_status(
                extraction_id, 
                status="processing", 
                progress=10
            )
            
            # Step 1: Extract text from files
            logger.info(f"Extracting text from {len(files)} files")
            extracted_texts = []
            
            for i, (file_content, file_name) in enumerate(zip(files, file_names)):
                try:
                    text = self._extract_text_from_file(file_content, file_name)
                    extracted_texts.append(text)
                    
                    # Update progress
                    progress = 10 + (i + 1) * 20 // len(files)
                    extraction_status_manager.update_status(extraction_id, progress=progress)
                    
                except Exception as e:
                    logger.error(f"Failed to extract text from {file_name}: {str(e)}")
                    continue
            
            if not extracted_texts:
                raise ValueError("Could not extract text from any uploaded files")
            
            # Combine all texts
            combined_text = "\n\n--- DOCUMENT SEPARATOR ---\n\n".join(extracted_texts)
            
            # Step 2: Send to Gemini for processing
            extraction_status_manager.update_status(extraction_id, progress=40)
            
            logger.info("Sending text to Gemini 2.5 Flash for processing")
            gemini_result = await self._process_with_gemini(combined_text, contract_type_hint)
            
            extraction_status_manager.update_status(extraction_id, progress=80)
            
            # Step 3: Parse and validate results
            parsed_result = self._parse_gemini_response(gemini_result)
            
            # Step 4: Determine final status
            missing_fields = self._get_missing_required_fields(parsed_result)
            
            if missing_fields:
                status = "partial"
                extraction_status_manager.update_status(
                    extraction_id,
                    status=status,
                    progress=100,
                    contract_data=parsed_result.dict(),
                    confidence_score=parsed_result.field_confidence.get('overall', 0.0),
                    missing_fields=missing_fields
                )
            else:
                status = "completed"
                extraction_status_manager.update_status(
                    extraction_id,
                    status=status,
                    progress=100,
                    contract_data=parsed_result.dict(),
                    confidence_score=parsed_result.field_confidence.get('overall', 0.0)
                )
            
            return {
                "status": status,
                "data": parsed_result.dict(),
                "missing_fields": missing_fields
            }
            
        except Exception as e:
            logger.error(f"Extraction failed for {extraction_id}: {str(e)}")
            
            # Determine specific error message
            error_msg = self._get_specific_error_message(e)
            
            extraction_status_manager.update_status(
                extraction_id,
                status="failed",
                progress=100,
                error_message=error_msg
            )
            
            return {
                "status": "failed",
                "error": error_msg
            }
    
    def _extract_text_from_file(self, file_content: bytes, file_name: str) -> str:
        """Extract text from PDF or DOCX file"""
        file_extension = file_name.lower().split('.')[-1]
        
        if file_extension == 'pdf':
            return self._extract_text_from_pdf(file_content)
        elif file_extension in ['docx', 'doc']:
            return self._extract_text_from_docx(file_content)
        else:
            raise ValueError(f"Unsupported file type: {file_extension}")
    
    def _extract_text_from_pdf(self, file_content: bytes) -> str:
        """Extract text from PDF using PyPDF2"""
        try:
            pdf_file = io.BytesIO(file_content)
            pdf_reader = PyPDF2.PdfReader(pdf_file)
            
            text = ""
            for page in pdf_reader.pages:
                text += page.extract_text() + "\n"
            
            if not text.strip():
                raise ValueError("PDF appears to be scanned or contains no extractable text")
            
            return text
        except Exception as e:
            raise ValueError(f"Could not extract text from PDF: {str(e)}")
    
    def _extract_text_from_docx(self, file_content: bytes) -> str:
        """Extract text from DOCX file"""
        try:
            docx_file = io.BytesIO(file_content)
            doc = Document(docx_file)
            
            text = ""
            for paragraph in doc.paragraphs:
                text += paragraph.text + "\n"
            
            if not text.strip():
                raise ValueError("DOCX file appears to be empty")
            
            return text
        except Exception as e:
            raise ValueError(f"Could not extract text from DOCX: {str(e)}")
    
    async def _process_with_gemini(
        self, 
        text: str, 
        contract_type_hint: Optional[ContractType] = None
    ) -> str:
        """Send text to Gemini 2.5 Flash for processing"""
        
        prompt = self._build_extraction_prompt(text, contract_type_hint)
        
        try:
            response = self.model.generate_content(prompt)
            return response.text
        except Exception as e:
            raise ValueError(f"Gemini API error: {str(e)}")
    
    def _build_extraction_prompt(self, text: str, contract_type_hint: Optional[ContractType] = None) -> str:
        """Build the prompt for Gemini extraction"""
        
        base_prompt = f"""
You are an expert legal document analyzer specializing in football/soccer contracts. 
Analyze the following contract text and extract structured data.

CONTRACT TEXT:
{text}

INSTRUCTIONS:
1. First, identify the contract type from these options:
   - mandate: Agent mandate/authorization
   - representation_agreement: Player representation contract
   - club_contract: Player to club contract
   - commission_agreement: Commission/fee agreement
   - commercial: Commercial/sponsorship agreement

2. Extract relevant fields based on the contract type:

For ALL contract types:
- start_date (YYYY-MM-DD format)
- end_date (YYYY-MM-DD format)
- player_name (full name)
- agent_name (if mentioned)
- notes (summary of key clauses and terms)

For representation_agreement:
- commission_percentage (%)
- exclusive (true/false)
- termination_fee (amount)

For mandate:
- coverage_region (list of countries/regions)
- exclusive (true/false)

For club_contract:
- club_name
- gross_salary (annual amount)
- signing_fee
- option_years (number)

For commission_agreement:
- commission_amounts (list with amounts and due dates)
- installments (payment schedule)

3. Provide confidence scores (0-100) for each extracted field
4. If a field cannot be found, set it to null
5. Include an overall confidence score

IMPORTANT: 
- Return ONLY valid JSON
- Use null for missing values
- Be conservative with confidence scores
- Include field_confidence object with scores for each field

{f"HINT: This appears to be a {contract_type_hint} contract." if contract_type_hint else ""}

Return the result as JSON in this exact format:
{{
    "contract_type": "mandate|representation_agreement|club_contract|commission_agreement|commercial",
    "start_date": "YYYY-MM-DD or null",
    "end_date": "YYYY-MM-DD or null",
    "player_name": "string or null",
    "agent_name": "string or null",
    "club_name": "string or null",
    "gross_salary": number or null,
    "signing_fee": number or null,
    "commission_percentage": number or null,
    "option_years": number or null,
    "coverage_region": ["region1", "region2"] or null,
    "exclusive": true/false or null,
    "termination_fee": number or null,
    "commission_amounts": [{"amount": number, "due_date": "YYYY-MM-DD", "description": "string"}] or null,
    "installments": [{"amount": number, "due_date": "YYYY-MM-DD"}] or null,
    "notes": "string summary of key terms",
    "field_confidence": {{
        "contract_type": 0-100,
        "start_date": 0-100,
        "end_date": 0-100,
        "player_name": 0-100,
        "overall": 0-100
    }}
}}
"""
        
        return base_prompt
    
    def _parse_gemini_response(self, response: str) -> ExtractedContractData:
        """Parse Gemini response into structured data"""
        try:
            # Extract JSON from response (in case there's extra text)
            json_start = response.find('{')
            json_end = response.rfind('}') + 1
            
            if json_start == -1 or json_end == 0:
                raise ValueError("No JSON found in Gemini response")
            
            json_str = response[json_start:json_end]
            data = json.loads(json_str)
            
            # Convert to our schema
            return ExtractedContractData(**data)
            
        except json.JSONDecodeError as e:
            raise ValueError(f"Invalid JSON in Gemini response: {str(e)}")
        except Exception as e:
            raise ValueError(f"Could not parse Gemini response: {str(e)}")
    
    def _get_missing_required_fields(self, data: ExtractedContractData) -> List[str]:
        """Determine which required fields are missing based on contract type"""
        missing = []
        
        # Basic required fields for all contracts
        if not data.contract_type:
            missing.append("contract_type")
        if not data.start_date:
            missing.append("start_date")
        if not data.end_date:
            missing.append("end_date")
        
        # Contract type specific requirements
        if data.contract_type == ContractType.club_contract:
            if not data.club_name:
                missing.append("club_name")
            if not data.gross_salary:
                missing.append("gross_salary")
        
        elif data.contract_type == ContractType.representation_agreement:
            if not data.commission_percentage:
                missing.append("commission_percentage")
        
        elif data.contract_type == ContractType.mandate:
            if not data.coverage_region:
                missing.append("coverage_region")
        
        elif data.contract_type == ContractType.commission_agreement:
            if not data.commission_amounts:
                missing.append("commission_amounts")
        
        return missing
    
    def _get_specific_error_message(self, error: Exception) -> str:
        """Convert generic errors to user-friendly messages"""
        error_str = str(error).lower()
        
        if "could not extract text" in error_str or "pdf appears to be scanned" in error_str:
            return "PDF appears to be corrupted or is a scanned document with poor quality"
        elif "docx file appears to be empty" in error_str:
            return "DOCX file appears to be empty or corrupted"
        elif "unsupported file type" in error_str:
            return "Unsupported file type. Please upload PDF or DOCX files only"
        elif "gemini api error" in error_str:
            return "Could not process document with AI service. Please try again"
        elif "no json found" in error_str or "invalid json" in error_str:
            return "Could not detect contract type or extract data from document"
        elif "timeout" in error_str:
            return "Document processing timed out. Please try with a smaller file"
        else:
            return "An unexpected error occurred during processing"


# Global instance
gemini_service = GeminiExtractionService()
