import asyncio
import uuid
from typing import Any, List, Optional
from fastapi import APIRouter, Depends, HTTPException, UploadFile, File, BackgroundTasks, Form
from sqlalchemy.orm import Session

from app.schemas.contract_extraction import (
    ContractExtractionRequest,
    ContractExtractionResult,
    ContractExtractionStatus,
    ContractCreationFromExtraction
)
from app.schemas.enums import ContractType
from app.schemas.contract import ContractCreate, Contract
from app import crud, models
from app.api import deps, utils
from app.services.gemini_extraction_service import gemini_service
from app.services.extraction_status import extraction_status_manager
from app.utils.cloud_storage import CloudStorageManager

router = APIRouter()


@router.post("/extract", response_model=ContractExtractionStatus)
async def extract_contract_data(
    background_tasks: BackgroundTasks,
    files: List[UploadFile] = File(...),
    contract_type: Optional[str] = Form(None),
    scan_mode: str = Form("extract_only"),
    player_id_override: Optional[str] = Form(None),
    current_user: models.User = Depends(deps.get_current_active_user),
    db: Session = Depends(deps.get_db),
) -> Any:
    """
    Extract contract data from uploaded files using Gemini 2.5 Flash
    
    - **files**: PDF or DOCX files (max 20MB each)
    - **contract_type**: Optional hint for contract type
    - **scan_mode**: "extract_only" or "extract_and_save"
    - **player_id_override**: Optional player ID if known
    """
    # Check permissions
    utils.check_module_access(current_user, "legal")
    utils.check_create(models.Contract, current_user)
    
    # Validate files
    if not files:
        raise HTTPException(status_code=400, detail="No files uploaded")
    
    if len(files) > 5:
        raise HTTPException(status_code=400, detail="Maximum 5 files allowed")
    
    file_contents = []
    file_names = []
    
    for file in files:
        # Check file size (20MB limit)
        if file.size and file.size > 20 * 1024 * 1024:
            raise HTTPException(
                status_code=400, 
                detail=f"File {file.filename} exceeds 20MB limit"
            )
        
        # Check file type
        if not file.filename:
            raise HTTPException(status_code=400, detail="File must have a name")
        
        file_ext = file.filename.lower().split('.')[-1]
        if file_ext not in ['pdf', 'docx', 'doc']:
            raise HTTPException(
                status_code=400, 
                detail=f"Unsupported file type: {file_ext}. Only PDF and DOCX files are supported"
            )
        
        # Read file content
        content = await file.read()
        file_contents.append(content)
        file_names.append(file.filename)
    
    # Validate contract type hint
    contract_type_enum = None
    if contract_type:
        try:
            contract_type_enum = ContractType(contract_type)
        except ValueError:
            raise HTTPException(
                status_code=400, 
                detail=f"Invalid contract type: {contract_type}"
            )
    
    # Validate player ID override
    player_id_uuid = None
    if player_id_override:
        try:
            player_id_uuid = uuid.UUID(player_id_override)
        except ValueError:
            raise HTTPException(
                status_code=400, 
                detail="Invalid player ID format"
            )
    
    # Create extraction job
    extraction_id = extraction_status_manager.create_extraction({
        "user_id": current_user.id,
        "file_names": file_names,
        "contract_type_hint": contract_type,
        "scan_mode": scan_mode,
        "player_id_override": player_id_override
    })
    
    # Start background processing
    background_tasks.add_task(
        _process_extraction_background,
        extraction_id,
        file_contents,
        file_names,
        contract_type_enum,
        scan_mode,
        player_id_uuid,
        current_user.id,
        current_user.organization_id,
        utils.can_access_sensitive(current_user)
    )
    
    return ContractExtractionStatus(
        extraction_id=extraction_id,
        status="processing",
        progress=0,
        message="Extraction started",
        estimated_completion=None  # We'll update this in the background task
    )


@router.get("/status/{extraction_id}", response_model=ContractExtractionResult)
async def get_extraction_status(
    extraction_id: str,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Get the status of a contract extraction job
    """
    utils.check_module_access(current_user, "legal")
    
    result = extraction_status_manager.get_status(extraction_id)
    if not result:
        raise HTTPException(
            status_code=404, 
            detail="Extraction job not found or has expired"
        )
    
    return result


@router.post("/create-from-extraction", response_model=Contract)
async def create_contract_from_extraction(
    request: ContractCreationFromExtraction,
    current_user: models.User = Depends(deps.get_current_active_user),
    db: Session = Depends(deps.get_db),
) -> Any:
    """
    Create a contract from extraction results
    """
    utils.check_module_access(current_user, "legal")
    utils.check_create(models.Contract, current_user)
    
    # Get extraction results
    extraction_result = extraction_status_manager.get_status(request.extraction_id)
    if not extraction_result:
        raise HTTPException(
            status_code=404, 
            detail="Extraction job not found or has expired"
        )
    
    if extraction_result.status not in ["completed", "partial"]:
        raise HTTPException(
            status_code=400, 
            detail=f"Cannot create contract from extraction with status: {extraction_result.status}"
        )
    
    if not extraction_result.contract_data:
        raise HTTPException(
            status_code=400, 
            detail="No contract data available from extraction"
        )
    
    # Check confidence score
    confidence = extraction_result.confidence_score or 0
    if confidence < 50 and not request.force_create:
        raise HTTPException(
            status_code=400, 
            detail=f"Extraction confidence too low ({confidence}%). Use force_create=true to override"
        )
    
    try:
        # Convert extraction data to contract creation schema
        contract_data = _convert_extraction_to_contract(
            extraction_result.contract_data,
            request.user_modifications
        )
        
        # Create contract using existing CRUD
        contract_type = ContractType(contract_data["contract_type"])
        specific_crud = _make_contract_crud(contract_type)
        
        contract_create = ContractCreate(**contract_data)
        contract = specific_crud.create_with_user(
            db=db,
            obj_in=contract_create,
            user=current_user,
        )
        
        return contract
        
    except Exception as e:
        raise HTTPException(
            status_code=500, 
            detail=f"Failed to create contract: {str(e)}"
        )


async def _process_extraction_background(
    extraction_id: str,
    file_contents: List[bytes],
    file_names: List[str],
    contract_type_hint: Optional[ContractType],
    scan_mode: str,
    player_id_override: Optional[uuid.UUID],
    user_id: uuid.UUID,
    organization_id: uuid.UUID,
    can_access_sense: bool
):
    """Background task for processing contract extraction"""
    try:
        # Get database session for entity matching
        from app.api.deps import get_db
        db = next(get_db())

        try:
            # Set timeout for the entire operation
            result = await asyncio.wait_for(
                gemini_service.extract_contract_data(
                    extraction_id=extraction_id,
                    files=file_contents,
                    file_names=file_names,
                    contract_type_hint=contract_type_hint,
                    db_session=db,
                    org_id=str(organization_id),
                    can_access_sense=can_access_sense
                ),
                timeout=180  # 3 minutes
            )
        finally:
            db.close()
        
        # If scan_mode is "extract_and_save", we could auto-create the contract here
        # For now, we'll leave it to the separate endpoint for better UX
        
    except asyncio.TimeoutError:
        extraction_status_manager.update_status(
            extraction_id,
            status="failed",
            progress=100,
            error_message="Processing timed out after 3 minutes"
        )
    except Exception as e:
        extraction_status_manager.update_status(
            extraction_id,
            status="failed",
            progress=100,
            error_message=f"Unexpected error: {str(e)}"
        )


def _convert_extraction_to_contract(
    extraction_data: dict, 
    user_modifications: Optional[dict] = None
) -> dict:
    """Convert extraction data to contract creation format"""
    
    # Start with extraction data
    contract_data = extraction_data.copy()
    
    # Apply user modifications if provided
    if user_modifications:
        contract_data.update(user_modifications)
    
    # Map extraction fields to contract schema fields
    mapped_data = {
        "contract_type": contract_data.get("contract_type"),
        "start_date": contract_data.get("start_date"),
        "end_date": contract_data.get("end_date"),
        "active_status": True,
        "currency": "EUR",  # Default currency
        "notes": contract_data.get("notes"),
        
        # Financial fields
        "gross_salary": contract_data.get("gross_salary"),
        "signing_fee": contract_data.get("signing_fee"),
        "pct_commission_agreed": contract_data.get("commission_percentage"),
        "termination_fee": contract_data.get("termination_fee"),
        "option_years": contract_data.get("option_years"),
        
        # Contract specific
        "exclusive": contract_data.get("exclusive"),
        "coverage": contract_data.get("coverage_region"),
        "installments": contract_data.get("installments"),
    }
    
    # Remove None values
    return {k: v for k, v in mapped_data.items() if v is not None}


def _make_contract_crud(contract_type: ContractType):
    """Create appropriate CRUD instance for contract type"""
    if model := next(
        filter(
            lambda x: x.__mapper_args__["polymorphic_identity"] == contract_type,
            {
                models.Mandate,
                models.RepresentationAgreement,
                models.ClubContract,
                models.CommissionAgreement,
                models.Commercial,
            },
        ),
        None,
    ):
        return crud.CRUDContract(model)
    raise ValueError("Invalid contract type")
