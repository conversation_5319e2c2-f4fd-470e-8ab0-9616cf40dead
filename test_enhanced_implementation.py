#!/usr/bin/env python3
"""
Enhanced test script for Legal OCR Contract Extractor with Entity Matching
"""

import sys
import os

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_enhanced_implementation():
    """Test the enhanced implementation with entity matching"""
    print("🚀 Testing Enhanced Legal OCR Contract Extractor Implementation")
    print()
    
    tests_passed = 0
    total_tests = 5
    
    # Test 1: Basic imports
    print("🧪 Test 1: Testing imports...")
    try:
        from app.config import settings
        print("✅ Config imported successfully")
        print(f"   - GEMINI_API_KEY configured: {'Yes' if settings.GEMINI_API_KEY else 'No'}")
        
        from app.schemas.contract_extraction import ExtractedContractData
        print("✅ Enhanced contract extraction schemas imported successfully")
        
        from app.services.extraction_status import extraction_status_manager
        print("✅ Extraction status manager imported successfully")
        
        from app.services.gemini_extraction_service import gemini_service
        print("✅ Gemini extraction service imported successfully")
        
        from app.services.entity_matching import entity_matcher
        print("✅ Entity matching service imported successfully")
        
        from app.api.endpoints.contract_extraction import router
        print("✅ Contract extraction API endpoints imported successfully")
        
        from app.main import app
        print("✅ Main application imported successfully")
        
        tests_passed += 1
        
    except Exception as e:
        print(f"❌ Import test failed: {str(e)}")
    
    print()
    
    # Test 2: Enhanced schema validation
    print("🧪 Test 2: Testing enhanced schema with entity matching...")
    try:
        # Test the enhanced schema with entity matching fields
        test_data = {
            "contract_type": "representation_agreement",
            "player_name": "John Doe",
            "agent_name": "Jane Smith",
            "suggested_player_id": "uuid-123",
            "suggested_player_name": "John A. Doe",
            "player_match_confidence": 85.5,
            "suggested_contact_id": "uuid-456", 
            "suggested_contact_name": "Jane Smith Jr.",
            "agent_match_confidence": 92.1,
            "commission_percentage": 5.0,
            "field_confidence": {
                "contract_type": 95,
                "player_name": 90,
                "agent_name": 88,
                "overall": 91
            }
        }
        
        extracted_data = ExtractedContractData(**test_data)
        print("✅ Enhanced schema validation passed")
        print(f"   - Player: '{extracted_data.player_name}' -> '{extracted_data.suggested_player_name}' ({extracted_data.player_match_confidence}%)")
        print(f"   - Agent: '{extracted_data.agent_name}' -> '{extracted_data.suggested_contact_name}' ({extracted_data.agent_match_confidence}%)")
        
        tests_passed += 1
        
    except Exception as e:
        print(f"❌ Enhanced schema test failed: {str(e)}")
    
    print()
    
    # Test 3: Entity matching functionality
    print("🧪 Test 3: Testing entity matching functionality...")
    try:
        from fuzzywuzzy import fuzz, process
        
        # Test fuzzy matching with football player names
        player_names = [
            "Cristiano Ronaldo",
            "Lionel Messi", 
            "Kylian Mbappé",
            "Erling Haaland",
            "Neymar Jr"
        ]
        
        # Test various name variations
        test_cases = [
            ("Cristiano", "Cristiano Ronaldo"),
            ("Messi", "Lionel Messi"),
            ("Kylian Mbappe", "Kylian Mbappé"),
            ("Haaland", "Erling Haaland")
        ]
        
        for query, expected in test_cases:
            best_match, confidence = process.extractOne(query, player_names, scorer=fuzz.ratio)
            print(f"   ✓ '{query}' -> '{best_match}' (confidence: {confidence}%)")
            
        print("✅ Entity matching functionality working correctly")
        tests_passed += 1
        
    except Exception as e:
        print(f"❌ Entity matching test failed: {str(e)}")
    
    print()
    
    # Test 4: Gemini service with entity matching support
    print("🧪 Test 4: Testing Gemini service...")
    try:
        print("✅ Gemini service initialized")
        print(f"   - Model: {gemini_service.model.model_name}")
        print("   - Timeout: 3 minutes")
        print("   - Entity matching: Enabled")
        
        tests_passed += 1
        
    except Exception as e:
        print(f"❌ Gemini service test failed: {str(e)}")
    
    print()
    
    # Test 5: API routes
    print("🧪 Test 5: Testing API routes...")
    try:
        route_paths = [route.path for route in router.routes if hasattr(route, 'path')]
        print("✅ Contract extraction routes found:")
        for path in route_paths:
            print(f"   - {path}")
        
        tests_passed += 1
        
    except Exception as e:
        print(f"❌ API routes test failed: {str(e)}")
    
    print()
    
    # Summary
    print(f"📊 Test Results: {tests_passed}/{total_tests} tests passed")
    
    if tests_passed == total_tests:
        print("🎉 All tests passed! Enhanced implementation is ready.")
        print()
        print("📋 Next steps:")
        print("1. Start the server: python -m uvicorn app.main:app --reload --port 6960")
        print("2. Test the enhanced API endpoints:")
        print("   - POST /api/v1/contract_extraction/extract")
        print("   - GET /api/v1/contract_extraction/status/{extraction_id}")
        print("   - POST /api/v1/contract_extraction/create-from-extraction")
        print("3. Check the API documentation at: http://localhost:6960/docs")
        print()
        print("🔧 ENHANCED FEATURES:")
        print("   - ✅ Gemini 2.5 Flash OCR for scanned PDFs")
        print("   - ✅ Fuzzy matching for player names")
        print("   - ✅ Fuzzy matching for agent/contact names")
        print("   - ✅ Confidence scores for entity matches")
        print("   - ✅ Original names preserved for user review")
        print("   - ✅ Automatic platform ID suggestions")
        return True
    else:
        print("❌ Some tests failed. Please check the errors above.")
        return False

if __name__ == "__main__":
    success = test_enhanced_implementation()
    sys.exit(0 if success else 1)
