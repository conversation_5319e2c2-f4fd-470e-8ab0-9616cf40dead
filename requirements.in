# API Server
fastapi>=0.115.0
uvicorn[standard]>=0.34.0
pydantic[email]>=1.10,<2.0
email-validator>=2.2.0
python-multipart

# JWT & Auth
fastapi-users[sqlalchemy, mongodb]>=14.0.0
fastapi-cache==0.1.0
passlib>=1.7.4
bcrypt>=4.2.1

# Database
dnspython>=2.7.0
sqlalchemy[asyncio]>=2.0,<3.0
asyncpg>=0.30.0
alembic
psycopg2>=2.9.10

# Google Cloud
google-cloud-storage>=2.19.0
google-cloud-firestore>=2.20.0
google-api-python-client>=2.159.0
google-auth>=2.37.0

# Redis Caching
aioredis<2

# Notifications
firebase-admin>=6.6.0
pyfcm>=2.0.7

# General Utilities
orjson>=3.10.14
python-dotenv>=1.0.1

# Data Processing
pandas>=2.2.3
numpy>=2.2.1

# HTML Rendering
Jinja2>=3.1.5
weasyprint>=63.1
tinycss2>=1.4.0

# Testing
attrs>=24.3.0
httpx>=0.28.1
pytest>=7.0.0
pytest-asyncio>=0.19.0

# Optional for extended support
fastjsonschema>=2.21.1
tinyhtml5>=2.0.0

# Legal OCR Contract Extractor
google-generativeai>=0.8.0
python-docx>=0.8.11
PyPDF2>=3.0.0
