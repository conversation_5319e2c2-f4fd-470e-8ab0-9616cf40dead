#!/usr/bin/env python3
"""
Test script for entity matching functionality
"""

import sys
import os

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_entity_matching():
    """Test the entity matching service"""
    try:
        print("🧪 Testing Entity Matching Service...")
        
        # Test imports
        print("  ✓ Testing imports...")
        from app.services.entity_matching import EntityMatcher
        print("  ✓ EntityMatcher imported successfully")
        
        # Test fuzzy matching
        print("  ✓ Testing fuzzy matching...")
        from fuzzywuzzy import fuzz, process
        
        # Test basic fuzzy matching
        test_names = ["<PERSON>", "<PERSON>", "<PERSON>"]
        query = "<PERSON>"
        
        best_match, confidence = process.extractOne(query, test_names, scorer=fuzz.ratio)
        print(f"  ✓ Fuzzy match test: '{query}' -> '{best_match}' (confidence: {confidence}%)")
        
        # Test EntityMatcher initialization
        print("  ✓ Testing EntityMatcher initialization...")
        matcher = EntityMatcher()
        print("  ✓ EntityMatcher initialized successfully")
        
        print("✅ All entity matching tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ Entity matching test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_entity_matching()
    sys.exit(0 if success else 1)
