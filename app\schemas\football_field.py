from datetime import datetime
from pydantic import BaseModel, Field
from typing import Optional, List, TYPE_CHECKING
from app.schemas.extended_base import (
    ExtendedUpdateBase,
    ExtendedCreateBase,
    ExtendedBase,
)
from app.schemas.logo_upload import LogoUpload
from app.schemas.team_info import TeamInfo
import uuid

from app.schemas.field_players import FieldPlayersCreate, FieldPlayers


class FootballFieldUpdate(ExtendedUpdateBase):
    name: Optional[str]
    club_name: Optional[str]
    for_date: Optional[datetime]
    formation: Optional[str]
    teamId: Optional[int]
    players: Optional["List[FieldPlayersCreate]"] = Field(default_factory=list)
    contact_name: Optional[str]
    contact_email: Optional[str]
    contact_phone: Optional[str]
    class Config:
        orm_mode = True
        use_cache = True


class FootballFieldAutofill(FootballFieldUpdate, ExtendedCreateBase):
    name: str
    club_name: Optional[str]
    for_date: Optional[datetime]
    formation: str
    teamId: int
    players: Optional["List[FieldPlayersCreate]"] = Field(default_factory=list)
    contact_name: Optional[str]
    contact_email: Optional[str]
    contact_phone: Optional[str]

class FootballFieldCreate(FootballFieldUpdate, ExtendedCreateBase):
    name: str
    club_name: Optional[str]
    for_date: Optional[datetime]
    formation: str
    teamId: Optional[int]
    players: Optional["List[FieldPlayersCreate]"] = Field(default_factory=list)
    id: Optional[uuid.UUID]
    contact_name: Optional[str]
    contact_email: Optional[str]
    contact_phone: Optional[str]

class FootballField(FootballFieldCreate, ExtendedBase):
    players: Optional["List[FieldPlayers]"] = Field(default_factory=list)
    team: Optional[TeamInfo]
    hide_field_view: Optional[bool] = False
    contact_name: Optional[str]
    contact_email: Optional[str]
    contact_phone: Optional[str]
    # uploads: Optional[LogoUpload]

    class Config:
        orm_mode = True
        use_cache = True


class FootballFieldViewAll(BaseModel):
    id: uuid.UUID
    players: int
    name: str
    last_updated: datetime
    team_name: Optional[str]
    created_by: str
    has_shared_link: Optional[bool]
    contact_name: Optional[str]
    contact_email: Optional[str]
    contact_phone: Optional[str]


LogoUpload.update_forward_refs(FootballField=FootballField)
