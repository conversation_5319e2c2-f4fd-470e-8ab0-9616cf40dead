from typing import Any, List, Optional

from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session

from app.schemas.contact import Contact, ContactUpdate, ContactCreate
from app import crud, models
from app.api import deps, utils

router = APIRouter()


@router.get("/", response_model=List[Contact])
def read_contacts(
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Retrieve contacts.
    """
    utils.check_get_all(Contact, current_user)
    # print(crud.contact.get_all_w_org(
    #     db, current_user.organization_id, utils.can_access_sensitive(current_user)
    # ))
    return crud.contact.get_all_w_org(
        db, current_user.organization_id, utils.can_access_sensitive(current_user)
    )


@router.post("/", response_model=Contact)
def create_contact(
    *,
    db: Session = Depends(deps.get_db),
    contact_in: Contact<PERSON>reate,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Create new contact.
    """
    utils.check_create(Contact, current_user)
    contact = crud.contact.create_with_user(
        db=db,
        obj_in=contact_in,
        user=current_user,
    )
    return contact


@router.put("/{id}", response_model=Contact)
def update_contact(
    *,
    db: Session = Depends(deps.get_db),
    id: str,
    contact_in: ContactUpdate,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Update an contact.
    """
    contact = crud.contact.get_by_org(db=db, id=id, org_id=current_user.organization_id)
    utils.check_modify(contact, current_user)
    contact = crud.contact.update(db=db, db_obj=contact, obj_in=contact_in)
    return contact


@router.get("/{id}", response_model=Contact)
def read_contact(
    *,
    db: Session = Depends(deps.get_db),
    id: str,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Get contact by ID.
    """
    contact = crud.contact.get_by_org(db=db, id=id, org_id=current_user.organization_id)
    if not contact:
        raise HTTPException(status_code=404, detail="Contact not found")
    utils.check_get_one(contact, current_user)
    return contact

@router.delete("/bulk-delete", response_model=List[Contact])
async def delete_multiple_contacts(
    *,
    db: Session = Depends(deps.get_db),
    ids: Optional[str] = None,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Delete multiple Contacts.
    """
    if ids is None:
        raise HTTPException(status_code=400, detail="No ids provided")

    ids_list = ids.split(',')
    contacts_out = []
    for id in ids_list:
        contact = crud.contact.get_by_org(db=db, id=id, org_id=current_user.organization_id)
        utils.check_delete(contact, current_user)
        contact_out = Contact.from_orm(contact)
        contacts_out.append(contact_out)
        crud.contact.remove(db=db, id=id)
    return contacts_out

@router.delete("/{id}", response_model=Contact)
def delete_contact(
    *,
    db: Session = Depends(deps.get_db),
    id: str,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Delete an contact.
    """
    contact = crud.contact.get_by_org(db=db, id=id, org_id=current_user.organization_id)
    utils.check_delete(contact, current_user)
    contact_out = Contact.from_orm(contact)
    crud.contact.remove(db=db, id=id)
    return contact_out
