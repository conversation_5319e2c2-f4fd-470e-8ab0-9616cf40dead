#!/usr/bin/env python3
"""
Test script to verify the Legal OCR Contract Extractor implementation
"""

import sys
import traceback

def test_imports():
    """Test all imports work correctly"""
    print("🧪 Testing imports...")
    
    try:
        # Test config
        from app.config import settings
        print("✅ Config imported successfully")
        print(f"   - GEMINI_API_KEY configured: {'Yes' if hasattr(settings, 'GEMINI_API_KEY') and settings.GEMINI_API_KEY else 'No'}")
        
        # Test schemas
        from app.schemas.contract_extraction import (
            ContractExtractionRequest,
            ContractExtractionResult,
            ExtractedContractData
        )
        print("✅ Contract extraction schemas imported successfully")
        
        # Test services
        from app.services.extraction_status import extraction_status_manager
        print("✅ Extraction status manager imported successfully")
        
        from app.services.gemini_extraction_service import gemini_service
        print("✅ Gemini extraction service imported successfully")
        
        # Test API endpoints
        from app.api.endpoints.contract_extraction import router
        print("✅ Contract extraction API endpoints imported successfully")
        
        # Test main app
        from app.main import app
        print("✅ Main application imported successfully")
        
        return True
        
    except Exception as e:
        print(f"❌ Import failed: {str(e)}")
        traceback.print_exc()
        return False

def test_status_manager():
    """Test the extraction status manager"""
    print("\n🧪 Testing extraction status manager...")
    
    try:
        from app.services.extraction_status import extraction_status_manager
        
        # Test creating extraction
        extraction_id = extraction_status_manager.create_extraction({"test": "data"})
        print(f"✅ Created extraction with ID: {extraction_id}")
        
        # Test getting status
        status = extraction_status_manager.get_status(extraction_id)
        print(f"✅ Retrieved status: {status.status}")
        
        # Test updating status
        success = extraction_status_manager.update_status(
            extraction_id, 
            status="completed", 
            progress=100,
            contract_data={"test": "result"}
        )
        print(f"✅ Updated status: {success}")
        
        # Test getting updated status
        updated_status = extraction_status_manager.get_status(extraction_id)
        print(f"✅ Updated status retrieved: {updated_status.status}")
        
        return True
        
    except Exception as e:
        print(f"❌ Status manager test failed: {str(e)}")
        traceback.print_exc()
        return False

def test_gemini_service():
    """Test Gemini service initialization"""
    print("\n🧪 Testing Gemini service...")
    
    try:
        from app.services.gemini_extraction_service import gemini_service
        
        # Check if service is properly initialized
        print(f"✅ Gemini service initialized")
        print(f"   - Model: {gemini_service.model.model_name if hasattr(gemini_service.model, 'model_name') else 'gemini-2.5-flash'}")
        print(f"   - Timeout: {gemini_service.timeout_minutes} minutes")
        
        return True
        
    except Exception as e:
        print(f"❌ Gemini service test failed: {str(e)}")
        traceback.print_exc()
        return False

def test_api_routes():
    """Test API routes are properly configured"""
    print("\n🧪 Testing API routes...")
    
    try:
        from app.main import app
        
        # Get all routes
        routes = []
        for route in app.routes:
            if hasattr(route, 'path'):
                routes.append(route.path)
        
        # Check for our new routes
        extraction_routes = [r for r in routes if 'contract_extraction' in r]
        
        if extraction_routes:
            print("✅ Contract extraction routes found:")
            for route in extraction_routes:
                print(f"   - {route}")
        else:
            print("❌ No contract extraction routes found")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ API routes test failed: {str(e)}")
        traceback.print_exc()
        return False

def main():
    """Run all tests"""
    print("🚀 Testing Legal OCR Contract Extractor Implementation\n")
    
    tests = [
        test_imports,
        test_status_manager,
        test_gemini_service,
        test_api_routes
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Implementation is ready.")
        print("\n📋 Next steps:")
        print("1. Start the server: python -m uvicorn app.main:app --reload --port 6960")
        print("2. Test the API endpoints:")
        print("   - POST /api/v1/contract_extraction/extract")
        print("   - GET /api/v1/contract_extraction/status/{extraction_id}")
        print("   - POST /api/v1/contract_extraction/create-from-extraction")
        print("3. Check the API documentation at: http://localhost:6960/docs")
        return True
    else:
        print("❌ Some tests failed. Please fix the issues before proceeding.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
