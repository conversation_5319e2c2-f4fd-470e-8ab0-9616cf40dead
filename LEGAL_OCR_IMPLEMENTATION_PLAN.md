# Legal OCR Contract Extractor - Implementation Plan

## 🎯 Overview
This document outlines the step-by-step implementation plan for the Legal OCR Contract Extractor MVP that will enable agents to upload contracts and automatically extract key data using OCR + AI to populate the Legal Module in EnskAI.

## 📋 Current System Analysis

### Existing Infrastructure
- ✅ **File Upload System**: Already implemented via `CloudStorageManager` (Google Cloud Storage)
- ✅ **Contract Management**: Complete CRUD operations for contracts with polymorphic models
- ✅ **OpenAI Integration**: API key already configured (`OPENAI_API_KEY`)
- ✅ **Contract Types**: 
  - `mandate`
  - `representation_agreement` 
  - `club_contract` (maps to "Player to Club Contract")
  - `commission_agreement`
  - `commercial`
- ✅ **File Support**: PDF, DOCX, PNG, JPEG already supported in upload endpoints

### Contract Schema Fields Available
- Basic: `start_date`, `end_date`, `active_status`, `currency`, `notes`
- Player/Staff: `player_id`, `staff_id`, `agent_id`, `agent_alt_name`
- Financial: `gross_salary`, `signing_fee`, `goal_bonus`, `assist_bonus`, `matches_played_bonus`
- Contract Specific: `option_years`, `installments`, `coverage`, `exclusive`, `termination_fee`
- Team: `teamId`, `minimum_fee_release_clause`

## 🚀 Implementation Steps

### Phase 1: Core Infrastructure Setup

#### Step 1.1: Add OCR and AI Dependencies
**Files to modify**: `requirements.in`
```bash
# Add to requirements.in
openai>=1.0.0
pytesseract>=0.3.10
pdf2image>=1.16.3
python-docx>=0.8.11
Pillow>=10.0.0  # Already exists, ensure version
```

#### Step 1.2: Create OCR Service Module
**New file**: `app/services/ocr_service.py`
- Implement OCR extraction for PDF and DOCX files
- Support both text-based PDFs and scanned documents
- Handle image preprocessing for better OCR accuracy

#### Step 1.3: Create AI Extraction Service
**New file**: `app/services/ai_extraction_service.py`
- OpenAI GPT integration for semantic field extraction
- Contract type classification
- Confidence scoring for extracted fields
- Entity linking (Player/Club matching)

#### Step 1.4: Create Contract Processing Service
**New file**: `app/services/contract_processing_service.py`
- Orchestrate OCR + AI pipeline
- Handle different contract types with specific field mappers
- Implement confidence thresholds and validation

### Phase 2: API Endpoints Development

#### Step 2.1: Create OCR Extraction Schemas
**New file**: `app/schemas/contract_extraction.py`
```python
class ContractExtractionRequest(BaseModel):
    contract_type: Optional[ContractType]
    scan_mode: Literal["extract_only", "extract_and_save"] = "extract_only"
    player_id_override: Optional[uuid.UUID]
    notify_users: Optional[List[str]]

class ContractExtractionResult(BaseModel):
    contract_type: ContractType
    confidence_score: float
    extracted_fields: Dict[str, Any]
    field_confidence: Dict[str, float]
    suggested_player: Optional[PlayerRecordShort]
    suggested_club: Optional[TeamInfo]
    notes: str
```

#### Step 2.2: Create OCR Extraction Endpoint
**New file**: `app/api/endpoints/contract_extraction.py`
- `POST /api/v1/contract-extraction/extract`
- Accept multiple files (PDF/DOCX, ≤ 10MB each)
- Return extracted data with confidence scores
- Support both "extract only" and "extract + save" modes

#### Step 2.3: Extend Contract Creation Endpoint
**Modify**: `app/api/endpoints/contracts.py`
- Add new endpoint: `POST /api/v1/contracts/create-from-extraction`
- Accept extraction results and create contract
- Link files if "extract_and_save" mode

### Phase 3: Contract Type Specific Extractors

#### Step 3.1: Representation Contract Extractor
**Add to**: `app/services/extractors/representation_extractor.py`
- Extract: Start Date, End Date, Player, Commission %, Agent name
- Generate notes: exclusivity, renewal, territory, early termination

#### Step 3.2: Mandate Extractor  
**Add to**: `app/services/extractors/mandate_extractor.py`
- Extract: Start Date, End Date, Player, Coverage region
- Generate notes: scope, exclusivity, revocability, client obligations

#### Step 3.3: Commission Agreement Extractor
**Add to**: `app/services/extractors/commission_extractor.py`
- Extract: Start Date, End Date, Player, Commission Amounts + Due Dates
- Handle conditional performance/future sale descriptions
- Generate notes: payout logic, delays, clawbacks

#### Step 3.4: Player to Club Contract Extractor
**Add to**: `app/services/extractors/club_contract_extractor.py`
- Extract: Start Date, End Date, Option Years, Gross Annual Salary, Signing Fee, Club Name
- Generate notes: option years, extensions, % of resale, bonuses, housing, etc.

### Phase 4: Entity Matching and Validation

#### Step 4.1: Player Matching Service
**New file**: `app/services/entity_matching/player_matcher.py`
- Fuzzy matching against existing player database
- Integration with Transfermarkt data if available
- Confidence scoring for matches

#### Step 4.2: Club Matching Service  
**New file**: `app/services/entity_matching/club_matcher.py`
- Match club names to existing `TeamInfo` records
- Handle variations in club naming
- Confidence scoring for matches

#### Step 4.3: Validation Service
**New file**: `app/services/validation_service.py`
- Validate extracted dates (logical start/end dates)
- Validate financial amounts (reasonable ranges)
- Cross-validate contract type with extracted content

### Phase 5: Frontend Integration Support

#### Step 5.1: Contract Type Classification Endpoint
**Add to**: `app/api/endpoints/contract_extraction.py`
- `POST /api/v1/contract-extraction/classify-type`
- Accept file and return predicted contract type
- Support pre-classification before full extraction

#### Step 5.2: Preview Extraction Endpoint
**Add to**: `app/api/endpoints/contract_extraction.py`
- `POST /api/v1/contract-extraction/preview`
- Return extraction preview without saving
- Allow user to review and modify before final save

### Phase 6: Error Handling and Monitoring

#### Step 6.1: Error Handling
- Graceful handling of OCR failures
- Fallback mechanisms for low-confidence extractions
- User-friendly error messages

#### Step 6.2: Logging and Monitoring
- Log extraction attempts and success rates
- Monitor confidence scores and accuracy
- Track processing times

## 🔧 Technical Implementation Details

### Database Changes
**No schema changes required** - existing contract models support all needed fields.

### File Processing Pipeline
1. **Upload** → Cloud Storage (existing)
2. **OCR** → Extract text from PDF/DOCX
3. **AI Processing** → Extract structured data
4. **Validation** → Validate and score confidence
5. **Entity Matching** → Link players/clubs
6. **Save/Return** → Based on scan mode

### AI Prompt Engineering
- Contract type-specific prompts
- Structured output formatting (JSON)
- Confidence scoring instructions
- Entity extraction guidelines

### Security Considerations
- File size limits (10MB per file)
- File type validation
- Secure file handling
- User permission checks

## 📊 Success Metrics
- Extraction accuracy per contract type
- Processing time per document
- User adoption rate
- Manual correction frequency

## 🎯 MVP Scope Boundaries
- **In Scope**: 4 contract types, basic OCR, AI extraction, file upload
- **Out of Scope**: Advanced OCR training, complex document layouts, batch processing UI
- **Future Enhancements**: Machine learning model training, advanced entity resolution, audit trails

## ⏱️ Estimated Timeline
- **Phase 1-2**: 1-2 weeks (Core infrastructure + API)
- **Phase 3**: 1 week (Contract extractors)
- **Phase 4**: 1 week (Entity matching)
- **Phase 5-6**: 1 week (Integration + Polish)
- **Total**: 4-5 weeks

## 🚦 Next Steps
1. Confirm technical approach and scope
2. Set up development environment with new dependencies
3. Begin with Phase 1 implementation
4. Iterative testing with sample contracts
5. Frontend integration planning
