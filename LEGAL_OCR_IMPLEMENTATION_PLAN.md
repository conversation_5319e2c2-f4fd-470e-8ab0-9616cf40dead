# Legal OCR Contract Extractor - Implementation Plan (Simplified)

## 🎯 Overview
This document outlines the **simplified** step-by-step implementation plan for the Legal OCR Contract Extractor MVP that will enable agents to upload contracts and automatically extract key data using Gemini 2.5 Flash AI to populate the Legal Module in EnskAI.

## 📋 Current System Analysis

### Existing Infrastructure
- ✅ **File Upload System**: Already implemented via `CloudStorageManager` (Google Cloud Storage)
- ✅ **Contract Management**: Complete CRUD operations for contracts with polymorphic models
- ✅ **Gemini Integration**: API key already configured (`GEMINI_API_KEY`)
- ✅ **Redis Cache**: Available for temporary status storage
- ✅ **Contract Types**:
  - `mandate`
  - `representation_agreement`
  - `club_contract` (maps to "Player to Club Contract")
  - `commission_agreement`
  - `commercial`
- ✅ **File Support**: PDF, DOCX, PNG, JPEG already supported in upload endpoints

### Contract Schema Fields Available
- Basic: `start_date`, `end_date`, `active_status`, `currency`, `notes`
- Player/Staff: `player_id`, `staff_id`, `agent_id`, `agent_alt_name`
- Financial: `gross_salary`, `signing_fee`, `goal_bonus`, `assist_bonus`, `matches_played_bonus`
- Contract Specific: `option_years`, `installments`, `coverage`, `exclusive`, `termination_fee`
- Team: `teamId`, `minimum_fee_release_clause`

## 🚀 Simplified Implementation Steps

### Phase 1: Core Infrastructure Setup (1-2 days)

#### Step 1.1: Add Minimal Dependencies
**Files to modify**: `requirements.in`
```bash
# Add minimal dependencies for Gemini integration
google-generativeai>=0.8.0  # For Gemini 2.5 Flash
python-docx>=0.8.11        # For DOCX processing
PyPDF2>=3.0.0              # For PDF text extraction (fallback)
```

#### Step 1.2: Add Gemini Configuration
**Modify**: `app/config/__init__.py`
```python
class GeminiSettings(BaseSettings):
    GEMINI_API_KEY: str
```

#### Step 1.3: Create Unified Gemini Extraction Service
**New file**: `app/services/gemini_extraction_service.py`
- Single service that handles both OCR and AI extraction using Gemini 2.5 Flash
- Direct PDF processing (send entire PDF to Gemini)
- Contract type classification and field extraction in one API call
- Confidence scoring and partial results handling

### Phase 2: Simple API Endpoints (1-2 days)

#### Step 2.1: Create Simple Extraction Schemas
**New file**: `app/schemas/contract_extraction.py`
```python
class ContractExtractionRequest(BaseModel):
    contract_type: Optional[ContractType]
    scan_mode: Literal["extract_only", "extract_and_save"] = "extract_only"
    player_id_override: Optional[uuid.UUID]

class ContractExtractionResult(BaseModel):
    extraction_id: str  # Simple UUID for tracking
    status: Literal["processing", "completed", "failed", "partial"]
    contract_data: Optional[Dict[str, Any]]
    confidence_score: Optional[float]
    partial_results: Optional[Dict[str, Any]]  # For partial extractions
    missing_fields: Optional[List[str]]  # Fields that couldn't be extracted
    error_message: Optional[str]
```

#### Step 2.2: Create Simple Extraction Endpoint
**New file**: `app/api/endpoints/contract_extraction.py`
```python
@router.post("/extract")
async def extract_contract_data(
    files: List[UploadFile],
    background_tasks: BackgroundTasks,
    request: ContractExtractionRequest
):
    # Validate files (PDF/DOCX, ≤ 20MB each, max 10 pages)
    # Start background processing with 2-3 minute timeout
    # Return extraction_id immediately for status polling
```

#### Step 2.3: Add Status Check Endpoint
```python
@router.get("/status/{extraction_id}")
async def get_extraction_status(extraction_id: str):
    # Return current status from in-memory dictionary
    # Include partial results if available
```

#### Step 2.4: Add Contract Creation from Extraction
**Modify**: `app/api/endpoints/contracts.py`
```python
@router.post("/create-from-extraction")
async def create_contract_from_extraction(
    extraction_id: str,
    user_modifications: Optional[Dict[str, Any]] = None
):
    # Create contract from extraction results
    # Apply user modifications if provided
    # Link files if "extract_and_save" mode
```

### Phase 3: Contract Type Extractors (2-3 days)

#### Step 3.1: Create Gemini Prompts for Each Contract Type
**Add to**: `app/services/prompts/contract_prompts.py`
- **Representation Contract**: Extract Start Date, End Date, Player, Commission %, Agent name
- **Mandate**: Extract Start Date, End Date, Player, Coverage region
- **Commission Agreement**: Extract Start Date, End Date, Player, Commission Amounts + Due Dates
- **Player to Club Contract**: Extract Start Date, End Date, Option Years, Gross Annual Salary, Signing Fee, Club Name
- **Leverage Gemini 2.5 Flash thinking capabilities** for better accuracy
- **Single JSON output format** for all contract types

#### Step 3.2: Simple Entity Matching
**Add to**: `app/services/entity_matching.py`
- Basic player name matching against existing database
- Simple club name matching to `TeamInfo` records
- Return confidence scores (exact match = 100%, fuzzy match = 50-90%)

#### Step 3.3: Basic Validation
**Add to**: `app/services/validation.py`
- Validate date formats and logical date ranges
- Basic financial amount validation (positive numbers)
- Contract type consistency checks

### Phase 4: Integration & Polish (1-2 days)

#### Step 4.1: In-Memory Status Management
**Add to**: `app/services/extraction_status.py`
```python
# Simple in-memory dictionary for tracking extraction status
extraction_status = {}  # {extraction_id: ExtractionResult}
```

#### Step 4.2: File Cleanup for "Extract Only" Mode
- Temporary file storage during processing
- Automatic cleanup after extraction completion
- Error handling for cleanup failures

#### Step 4.3: Background Task Implementation
**Modify**: `app/services/gemini_extraction_service.py`
- Implement 2-3 minute timeout for processing
- Handle partial results when some fields fail
- Update in-memory status throughout processing

#### Step 4.4: Error Handling
- Return partial results when possible
- Clear error messages for users
- Graceful handling of Gemini API failures

## 🔧 Technical Implementation Details

### Simplified Architecture
- **Single Service**: `GeminiExtractionService` handles everything
- **Direct PDF Processing**: Send entire PDF to Gemini 2.5 Flash
- **In-Memory Status**: Simple dictionary for tracking extraction progress
- **Background Tasks**: FastAPI BackgroundTasks for async processing

### File Processing Pipeline (Simplified)
1. **Upload** → Temporary storage during processing
2. **Gemini Processing** → Send entire PDF to Gemini 2.5 Flash API
3. **Parse Results** → Extract structured data and confidence scores
4. **Entity Matching** → Basic player/club matching
5. **Return/Save** → Based on scan mode, cleanup temp files

### Gemini 2.5 Flash Integration
- **Single API Call**: OCR + extraction + classification in one request
- **Thinking Capabilities**: Leverage enhanced reasoning for better accuracy
- **JSON Output**: Structured response with confidence scores
- **Timeout**: 2-3 minutes per document processing

### Status Management
```python
# In-memory status tracking
extraction_status = {
    "extraction_id": {
        "status": "processing|completed|failed|partial",
        "progress": 0-100,
        "results": {...},
        "error": "...",
        "created_at": datetime,
        "completed_at": datetime
    }
}
```

### Security Considerations
- File size limits (20MB per file, max 10 pages)
- File type validation (PDF, DOCX only)
- Temporary file cleanup
- User permission checks (existing system)

## 📊 Success Metrics
- Extraction accuracy per contract type (target: >85%)
- Processing time per document (target: <60 seconds)
- Partial extraction success rate
- User satisfaction with extracted data

## 🎯 Simplified MVP Scope
- **In Scope**:
  - 4 contract types with Gemini 2.5 Flash
  - Direct PDF processing
  - Basic entity matching
  - Partial results handling
  - Simple async processing
- **Out of Scope**:
  - Complex queue systems
  - Advanced error recovery
  - Detailed audit trails
  - Batch processing
- **Future Enhancements**:
  - Redis-based status storage
  - Advanced entity resolution
  - Machine learning improvements
  - Detailed analytics

## ⏱️ Revised Timeline
- **Phase 1**: 1-2 days (Gemini integration + config)
- **Phase 2**: 1-2 days (Simple API endpoints)
- **Phase 3**: 2-3 days (Contract type extractors)
- **Phase 4**: 1-2 days (Integration + polish)
- **Total**: 1-2 weeks

## 🚦 Next Steps
1. ✅ Confirm simplified approach
2. Add Gemini dependencies to requirements
3. Configure Gemini API settings
4. Implement core extraction service
5. Create API endpoints
6. Test with sample contracts
