#
# This file is autogenerated by pip-compile with Python 3.10
# by the following command:
#
#    pip-compile requirements.in
#
aiohappyeyeballs==2.4.4
    # via aiohttp
aiohttp==3.11.11
    # via pyfcm
aioredis==1.3.1
    # via
    #   -r requirements.in
    #   fastapi-cache
aiosignal==1.3.2
    # via aiohttp
alembic==1.14.0
    # via -r requirements.in
anyio==4.8.0
    # via
    #   httpx
    #   starlette
    #   watchfiles
argon2-cffi==23.1.0
    # via pwdlib
argon2-cffi-bindings==21.2.0
    # via argon2-cffi
async-timeout==5.0.1
    # via
    #   aiohttp
    #   aioredis
    #   asyncpg
asyncpg==0.30.0
    # via -r requirements.in
attrs==24.3.0
    # via
    #   -r requirements.in
    #   aiohttp
bcrypt==4.2.1
    # via
    #   -r requirements.in
    #   pwdlib
brotli==1.1.0
    # via fonttools
cachecontrol==0.14.2
    # via firebase-admin
cachetools==5.5.0
    # via google-auth
certifi==2024.12.14
    # via
    #   httpcore
    #   httpx
    #   requests
cffi==1.17.1
    # via
    #   argon2-cffi-bindings
    #   cryptography
    #   weasyprint
charset-normalizer==3.4.1
    # via requests
click==8.1.8
    # via uvicorn
colorama==0.4.6
    # via
    #   click
    #   pytest
    #   uvicorn
cryptography==44.0.0
    # via pyjwt
cssselect2==0.7.0
    # via weasyprint
dnspython==2.7.0
    # via
    #   -r requirements.in
    #   email-validator
email-validator==2.2.0
    # via
    #   -r requirements.in
    #   fastapi-users
    #   pydantic
exceptiongroup==1.2.2
    # via
    #   anyio
    #   pytest
fastapi==0.115.6
    # via
    #   -r requirements.in
    #   fastapi-users
fastapi-cache==0.1.0
    # via -r requirements.in
fastapi-users[mongodb,sqlalchemy]==14.0.1
    # via
    #   -r requirements.in
    #   fastapi-users-db-sqlalchemy
fastapi-users-db-sqlalchemy==7.0.0
    # via fastapi-users
fastjsonschema==2.21.1
    # via -r requirements.in
firebase-admin==6.6.0
    # via -r requirements.in
fonttools[woff]==4.55.3
    # via weasyprint
frozenlist==1.5.0
    # via
    #   aiohttp
    #   aiosignal
google-api-core[grpc]==2.24.0
    # via
    #   firebase-admin
    #   google-api-python-client
    #   google-cloud-core
    #   google-cloud-firestore
    #   google-cloud-storage
google-api-python-client==2.159.0
    # via
    #   -r requirements.in
    #   firebase-admin
google-auth==2.37.0
    # via
    #   -r requirements.in
    #   google-api-core
    #   google-api-python-client
    #   google-auth-httplib2
    #   google-cloud-core
    #   google-cloud-firestore
    #   google-cloud-storage
    #   pyfcm
google-auth-httplib2==0.2.0
    # via google-api-python-client
google-cloud-core==2.4.1
    # via
    #   google-cloud-firestore
    #   google-cloud-storage
google-cloud-firestore==2.20.0
    # via
    #   -r requirements.in
    #   firebase-admin
google-cloud-storage==2.19.0
    # via
    #   -r requirements.in
    #   firebase-admin
google-crc32c==1.6.0
    # via
    #   google-cloud-storage
    #   google-resumable-media
google-resumable-media==2.7.2
    # via google-cloud-storage
googleapis-common-protos==1.66.0
    # via
    #   google-api-core
    #   grpcio-status
greenlet==3.1.1
    # via sqlalchemy
grpcio==1.69.0
    # via
    #   google-api-core
    #   grpcio-status
grpcio-status==1.69.0
    # via google-api-core
h11==0.14.0
    # via
    #   httpcore
    #   uvicorn
hiredis==3.1.0
    # via aioredis
httpcore==1.0.7
    # via httpx
httplib2==0.22.0
    # via
    #   google-api-python-client
    #   google-auth-httplib2
httptools==0.6.4
    # via uvicorn
httpx==0.28.1
    # via -r requirements.in
idna==3.10
    # via
    #   anyio
    #   email-validator
    #   httpx
    #   requests
    #   yarl
iniconfig==2.0.0
    # via pytest
jinja2==3.1.5
    # via -r requirements.in
makefun==1.15.6
    # via fastapi-users
mako==1.3.8
    # via alembic
markupsafe==3.0.2
    # via
    #   jinja2
    #   mako
msgpack==1.1.0
    # via cachecontrol
multidict==6.1.0
    # via
    #   aiohttp
    #   yarl
numpy==2.2.1
    # via
    #   -r requirements.in
    #   pandas
orjson==3.10.14
    # via -r requirements.in
packaging==24.2
    # via pytest
pandas==2.2.3
    # via -r requirements.in
passlib==1.7.4
    # via -r requirements.in
pillow==11.1.0
    # via weasyprint
pluggy==1.5.0
    # via pytest
propcache==0.2.1
    # via
    #   aiohttp
    #   yarl
proto-plus==1.25.0
    # via
    #   google-api-core
    #   google-cloud-firestore
protobuf==5.29.3
    # via
    #   google-api-core
    #   google-cloud-firestore
    #   googleapis-common-protos
    #   grpcio-status
    #   proto-plus
psycopg2==2.9.10
    # via -r requirements.in
pwdlib[argon2,bcrypt]==0.2.1
    # via fastapi-users
pyasn1==0.6.1
    # via
    #   pyasn1-modules
    #   rsa
pyasn1-modules==0.4.1
    # via google-auth
pycparser==2.22
    # via cffi
pydantic[email]==1.10.21
    # via
    #   -r requirements.in
    #   fastapi
pydyf==0.11.0
    # via weasyprint
pyfcm==2.0.7
    # via -r requirements.in
pyjwt[crypto]==2.10.1
    # via
    #   fastapi-users
    #   firebase-admin
pyparsing==3.2.1
    # via httplib2
pyphen==0.17.0
    # via weasyprint
pytest==8.3.4
    # via
    #   -r requirements.in
    #   pytest-asyncio
pytest-asyncio==0.25.2
    # via -r requirements.in
python-dateutil==2.9.0.post0
    # via pandas
python-dotenv==1.0.1
    # via
    #   -r requirements.in
    #   uvicorn
python-multipart==0.0.20
    # via
    #   -r requirements.in
    #   fastapi-users
pytz==2024.2
    # via pandas
pyyaml==6.0.2
    # via uvicorn
requests==2.32.3
    # via
    #   cachecontrol
    #   google-api-core
    #   google-cloud-storage
    #   pyfcm
rsa==4.9
    # via google-auth
six==1.17.0
    # via python-dateutil
sniffio==1.3.1
    # via anyio
sqlalchemy[asyncio]==2.0.37
    # via
    #   -r requirements.in
    #   alembic
    #   fastapi-users-db-sqlalchemy
starlette==0.41.3
    # via fastapi
tinycss2==1.4.0
    # via
    #   -r requirements.in
    #   cssselect2
    #   weasyprint
tinyhtml5==2.0.0
    # via
    #   -r requirements.in
    #   weasyprint
tomli==2.2.1
    # via pytest
typing-extensions==4.12.2
    # via
    #   alembic
    #   anyio
    #   fastapi
    #   multidict
    #   pydantic
    #   sqlalchemy
    #   uvicorn
tzdata==2024.2
    # via pandas
uritemplate==4.1.1
    # via google-api-python-client
urllib3==2.3.0
    # via
    #   pyfcm
    #   requests
uvicorn[standard]==0.34.0
    # via -r requirements.in
watchfiles==1.0.4
    # via uvicorn
weasyprint==63.1
    # via -r requirements.in
webencodings==0.5.1
    # via
    #   cssselect2
    #   tinycss2
    #   tinyhtml5
websockets==14.1
    # via uvicorn
yarl==1.18.3
    # via aiohttp
zopfli==0.2.3.post1
    # via fonttools
pyotp==2.9.0
sendgrid==6.11.0
msal==1.31.1
hubspot-api-client==11.1.0
twilio
openpyxl