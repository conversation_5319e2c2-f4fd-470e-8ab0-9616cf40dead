import logging
from typing import Optional, Dict, Any, List, Tuple
from sqlalchemy.orm import Session
from fuzzywuzzy import fuzz
from fuzzywuzzy import process

from app import crud, models
# Note: ConstrolStage enum not needed for this implementation

logger = logging.getLogger(__name__)


class EntityMatcher:
    """
    Service for matching extracted contract names to existing platform entities
    """
    
    def __init__(self):
        self.min_similarity_threshold = 0  # Always return best match
    
    def match_player(
        self, 
        extracted_player_name: str, 
        db: Session, 
        org_id: str,
        can_access_sense: bool
    ) -> Dict[str, Any]:
        """
        Match extracted player name to existing player records
        
        Returns:
        {
            "suggested_player_id": "uuid or None",
            "suggested_player_name": "matched name or None", 
            "player_match_confidence": float
        }
        """
        try:
            if not extracted_player_name or not extracted_player_name.strip():
                return {
                    "suggested_player_id": None,
                    "suggested_player_name": None,
                    "player_match_confidence": 0.0
                }
            
            # Get all players with the specified filters
            filters = {
                "control_stage": ['singed', 'mandate', 'mandate_on_demand'],  # Note: 'singed' is the actual enum value (typo in enum)
                "active": True,
            }
            
            logger.info(f"Searching for player match: '{extracted_player_name}'")
            
            players = crud.player_record.get_all_with_filters(
                db, org_id, can_access_sense, filters
            )
            
            if not players:
                logger.info("No players found in database")
                return {
                    "suggested_player_id": None,
                    "suggested_player_name": None,
                    "player_match_confidence": 0.0
                }
            
            # Build list of player names for fuzzy matching
            player_choices = []
            player_map = {}
            
            for player in players:
                if hasattr(player, 'player_info') and player.player_info:
                    # Combine firstName and lastName
                    first_name = getattr(player.player_info, 'firstName', '') or ''
                    last_name = getattr(player.player_info, 'lastName', '') or ''
                    full_name = f"{first_name} {last_name}".strip()
                    
                    if full_name:
                        player_choices.append(full_name)
                        # Map full name to player record (use most recent if duplicates)
                        if full_name not in player_map or player.created_at > player_map[full_name].created_at:
                            player_map[full_name] = player
            
            if not player_choices:
                logger.info("No valid player names found")
                return {
                    "suggested_player_id": None,
                    "suggested_player_name": None,
                    "player_match_confidence": 0.0
                }
            
            # Find best match using fuzzy matching
            best_match, confidence = process.extractOne(
                extracted_player_name, 
                player_choices,
                scorer=fuzz.ratio
            )
            
            matched_player = player_map[best_match]
            
            logger.info(f"Best player match: '{best_match}' (confidence: {confidence}%)")
            
            return {
                "suggested_player_id": str(matched_player.id),
                "suggested_player_name": best_match,
                "player_match_confidence": float(confidence)
            }
            
        except Exception as e:
            logger.error(f"Error matching player '{extracted_player_name}': {str(e)}")
            return {
                "suggested_player_id": None,
                "suggested_player_name": None,
                "player_match_confidence": 0.0
            }
    
    def match_contact(
        self, 
        extracted_agent_name: str, 
        db: Session, 
        org_id: str
    ) -> Dict[str, Any]:
        """
        Match extracted agent name to existing contacts
        
        Returns:
        {
            "suggested_contact_id": "uuid or None",
            "suggested_contact_name": "matched name or None",
            "agent_match_confidence": float
        }
        """
        try:
            if not extracted_agent_name or not extracted_agent_name.strip():
                return {
                    "suggested_contact_id": None,
                    "suggested_contact_name": None,
                    "agent_match_confidence": 0.0
                }
            
            logger.info(f"Searching for contact match: '{extracted_agent_name}'")
            
            # Get all contacts for the organization
            contacts = crud.contact.get_all_w_org(db, org_id)
            
            if not contacts:
                logger.info("No contacts found in database")
                return {
                    "suggested_contact_id": None,
                    "suggested_contact_name": None,
                    "agent_match_confidence": 0.0
                }
            
            # Build list of contact names for fuzzy matching
            contact_choices = []
            contact_map = {}
            
            for contact in contacts:
                # Get contact name (adjust field names based on your Contact model)
                contact_name = self._get_contact_name(contact)
                
                if contact_name:
                    contact_choices.append(contact_name)
                    # Map name to contact record (use most recent if duplicates)
                    if contact_name not in contact_map or contact.created_at > contact_map[contact_name].created_at:
                        contact_map[contact_name] = contact
            
            if not contact_choices:
                logger.info("No valid contact names found")
                return {
                    "suggested_contact_id": None,
                    "suggested_contact_name": None,
                    "agent_match_confidence": 0.0
                }
            
            # Find best match using fuzzy matching
            best_match, confidence = process.extractOne(
                extracted_agent_name, 
                contact_choices,
                scorer=fuzz.ratio
            )
            
            matched_contact = contact_map[best_match]
            
            logger.info(f"Best contact match: '{best_match}' (confidence: {confidence}%)")
            
            return {
                "suggested_contact_id": str(matched_contact.id),
                "suggested_contact_name": best_match,
                "agent_match_confidence": float(confidence)
            }
            
        except Exception as e:
            logger.error(f"Error matching contact '{extracted_agent_name}': {str(e)}")
            return {
                "suggested_contact_id": None,
                "suggested_contact_name": None,
                "agent_match_confidence": 0.0
            }
    
    def _get_contact_name(self, contact) -> Optional[str]:
        """
        Extract the full name from a contact record
        Adjust this method based on your Contact model structure
        """
        try:
            # Common contact name field patterns - adjust based on your model
            if hasattr(contact, 'full_name') and contact.full_name:
                return contact.full_name.strip()
            
            if hasattr(contact, 'name') and contact.name:
                return contact.name.strip()
            
            # If separate first/last name fields exist
            first_name = ''
            last_name = ''
            
            if hasattr(contact, 'first_name'):
                first_name = contact.first_name or ''
            if hasattr(contact, 'last_name'):
                last_name = contact.last_name or ''
                
            if first_name or last_name:
                return f"{first_name} {last_name}".strip()
            
            # If email is used as identifier
            if hasattr(contact, 'email') and contact.email:
                return contact.email.strip()
            
            return None
            
        except Exception as e:
            logger.error(f"Error extracting contact name: {str(e)}")
            return None
    
    def match_entities(
        self, 
        extracted_data: Dict[str, Any], 
        db: Session, 
        org_id: str,
        can_access_sense: bool
    ) -> Dict[str, Any]:
        """
        Match both player and contact entities from extracted contract data
        
        Returns enhanced extraction data with entity matches
        """
        result = extracted_data.copy()
        
        # Match player if player_name was extracted
        if extracted_data.get('player_name'):
            player_match = self.match_player(
                extracted_data['player_name'], 
                db, 
                org_id, 
                can_access_sense
            )
            result.update(player_match)
        else:
            result.update({
                "suggested_player_id": None,
                "suggested_player_name": None,
                "player_match_confidence": 0.0
            })
        
        # Match contact/agent if agent_name was extracted
        if extracted_data.get('agent_name'):
            contact_match = self.match_contact(
                extracted_data['agent_name'], 
                db, 
                org_id
            )
            result.update(contact_match)
        else:
            result.update({
                "suggested_contact_id": None,
                "suggested_contact_name": None,
                "agent_match_confidence": 0.0
            })
        
        return result


# Global instance
entity_matcher = EntityMatcher()
