import uuid
from typing import Optional
from pydantic import BaseModel
from app.schemas.enums import ContactType
from app.schemas.extended_base import (
    ExtendedBase,
    ExtendedUpdateBase,
    ExtendedCreateBase,
)


class ContactUpdate(ExtendedUpdateBase):
    id: Optional[uuid.UUID]
    first_name: Optional[str]
    last_name: Optional[str]
    contact_type: Optional[ContactType]
    title: Optional[str]
    email: Optional[str]
    contact_organization: Optional[str]
    phone_number: Optional[str]
    owner: Optional[str]


class ContactCreate(ExtendedCreateBase):
    first_name: str
    last_name: str
    contact_type: ContactType
    title: Optional[str] = ''
    email: Optional[str]
    contact_organization: Optional[str] = ''
    phone_number: Optional[str]
    owner: Optional[str]


class Contact(ContactCreate, ExtendedBase):
    ...


class ContactShort(BaseModel):
    id: uuid.UUID
    first_name: str
    last_name: str
    contact_organization: str
    contact_type: ContactType
    phone_number: Optional[str]
    class Config:
        orm_mode = True
        use_cache=True
    